# Pop Mart 预订助手

一个简单的Android应用，用于打开Pop Mart预订网站。

## 功能特点

- ✅ 内置WebView浏览器
- ✅ 自动加载Pop Mart预订页面
- ✅ 支持JavaScript
- ✅ 完整的网页浏览功能
- ✅ 返回键支持

## 技术栈

- **Android**: 原生Android开发
- **Kotlin**: 主要编程语言
- **WebView**: 内置浏览器组件
- **Material Design**: UI设计

## 项目结构

```
PopMartBooking/
├── app/
│   ├── src/main/
│   │   ├── java/com/popmart/booking/
│   │   │   └── MainActivity.kt          # 主Activity
│   │   ├── res/
│   │   │   ├── layout/
│   │   │   │   └── activity_main.xml    # 主布局
│   │   │   ├── values/
│   │   │   │   ├── strings.xml          # 字符串资源
│   │   │   │   ├── colors.xml           # 颜色资源
│   │   │   │   └── themes.xml           # 主题样式
│   │   │   └── AndroidManifest.xml      # 应用清单
│   │   └── build.gradle                 # 应用构建配置
│   ├── build.gradle                     # 项目构建配置
│   └── settings.gradle                  # Gradle设置
```

## 安装和运行

### 前提条件

- Android Studio Arctic Fox 或更高版本
- Android SDK API 24 (Android 7.0) 或更高版本
- Kotlin 1.8.20 或更高版本

### 构建步骤

1. **克隆或下载项目**
   ```bash
   # 如果是从Git克隆
   git clone <repository-url>
   cd PopMartBooking
   ```

2. **在Android Studio中打开项目**
   - 启动Android Studio
   - 选择 "Open an existing Android Studio project"
   - 选择项目根目录

3. **同步项目**
   - Android Studio会自动提示同步Gradle
   - 点击 "Sync Now" 等待同步完成

4. **运行应用**
   - 连接Android设备或启动模拟器
   - 点击 "Run" 按钮或按 Shift+F10

## 使用说明

1. **启动应用**: 点击应用图标启动
2. **自动加载**: 应用会自动加载Pop Mart预订页面
3. **浏览网页**: 可以正常浏览和操作网页
4. **返回导航**: 使用返回键可以在网页历史中导航

## 配置说明

### 目标网站
- 默认URL: `https://popmartth.rocket-booking.app/booking`
- 可在 `MainActivity.kt` 中的 `BOOKING_URL` 常量修改

### WebView设置
- JavaScript: 已启用
- DOM存储: 已启用
- 混合内容: 允许
- 用户代理: 模拟Chrome移动浏览器

## 权限说明

应用需要以下权限：
- `INTERNET`: 访问网络
- `ACCESS_NETWORK_STATE`: 检查网络状态

## 故障排除

### 常见问题

1. **网页加载失败**
   - 检查网络连接
   - 确认目标网站可访问
   - 查看Logcat中的错误信息

2. **JavaScript不工作**
   - 确认WebView设置中JavaScript已启用
   - 检查网站是否有JavaScript错误

3. **页面显示异常**
   - 尝试清除应用数据
   - 检查用户代理字符串设置

### 调试信息

应用会在Logcat中输出详细的调试信息：
- 页面加载状态
- 网络请求
- JavaScript错误
- WebView事件

## 下一步开发

这是一个基础框架，后续可以添加：
- 自动化操作功能
- 倒计时监控
- 按钮状态检测
- 精确点击功能
- 用户界面增强

## 许可证

本项目仅供学习和研究使用。
