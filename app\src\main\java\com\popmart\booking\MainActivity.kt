package com.popmart.booking

import android.annotation.SuppressLint
import android.os.Bundle
import android.util.Log
import android.view.View
import android.webkit.*
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import kotlinx.coroutines.launch

class MainActivity : AppCompatActivity(), AutomationManager.AutomationCallback {
    
    private lateinit var webView: WebView
    private lateinit var automationManager: AutomationManager
    private val TAG = "MainActivity"
    
    // Pop Mart 预订页面URL
    private val BOOKING_URL = "https://popmartth.rocket-booking.app/booking"
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)
        
        initializeWebView()
        initializeAutomation()
        loadBookingPage()
    }
    
    @SuppressLint("SetJavaScriptEnabled")
    private fun initializeWebView() {
        webView = findViewById(R.id.webView)
        
        webView.settings.apply {
            javaScriptEnabled = true
            domStorageEnabled = true
            allowFileAccess = true
            allowContentAccess = true
            allowFileAccessFromFileURLs = true
            allowUniversalAccessFromFileURLs = true
            mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
            cacheMode = WebSettings.LOAD_NO_CACHE
            userAgentString = "Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36"
        }
        
        webView.webViewClient = CustomWebViewClient()
        webView.webChromeClient = WebChromeClient()
        
        // 添加JavaScript接口用于调试
        webView.addJavascriptInterface(WebAppInterface(), "Android")
    }
    
    private fun initializeAutomation() {
        automationManager = AutomationManager()
        automationManager.setCallback(this)
        
        // 设置网络延迟补偿（可根据实际网络情况调整）
        automationManager.setNetworkDelayCompensation(30)
    }
    
    private fun loadBookingPage() {
        Log.i(TAG, "Loading booking page: $BOOKING_URL")
        webView.loadUrl(BOOKING_URL)
    }
    
    inner class CustomWebViewClient : WebViewClient() {
        
        override fun onPageStarted(view: WebView?, url: String?, favicon: android.graphics.Bitmap?) {
            super.onPageStarted(view, url, favicon)
            Log.i(TAG, "Page started loading: $url")
        }
        
        override fun onPageFinished(view: WebView?, url: String?) {
            super.onPageFinished(view, url)
            Log.i(TAG, "Page finished loading: $url")
            
            if (url?.contains("booking") == true) {
                // 页面加载完成后，等待2秒开始监控
                view?.postDelayed({
                    startAutomationMonitoring()
                }, 2000)
            }
        }
        
        override fun onReceivedError(view: WebView?, request: WebResourceRequest?, error: WebResourceError?) {
            super.onReceivedError(view, request, error)
            Log.e(TAG, "WebView error: ${error?.description}")
        }
        
        override fun shouldOverrideUrlLoading(view: WebView?, request: WebResourceRequest?): Boolean {
            val url = request?.url?.toString()
            Log.i(TAG, "Navigating to: $url")
            return false // 让WebView处理导航
        }
    }
    
    inner class WebAppInterface {
        @JavascriptInterface
        fun log(message: String) {
            Log.d(TAG, "JS Log: $message")
        }
        
        @JavascriptInterface
        fun showToast(message: String) {
            runOnUiThread {
                Toast.makeText(this@MainActivity, message, Toast.LENGTH_SHORT).show()
            }
        }
    }
    
    private fun startAutomationMonitoring() {
        Log.i(TAG, "Starting automation monitoring")
        runOnUiThread {
            Toast.makeText(this, "开始监控倒计时和按钮状态", Toast.LENGTH_SHORT).show()
        }
        automationManager.startMonitoring(webView)
    }
    
    // AutomationManager.AutomationCallback 实现
    override fun onCountdownDetected(remainingMs: Long) {
        val seconds = remainingMs / 1000
        Log.d(TAG, "Countdown detected: ${seconds}s remaining")
        
        runOnUiThread {
            // 更新UI显示倒计时
            if (seconds <= 10) {
                Toast.makeText(this, "倒计时: ${seconds}秒", Toast.LENGTH_SHORT).show()
            }
        }
    }
    
    override fun onButtonStateChanged(isClickable: Boolean) {
        Log.i(TAG, "Button state changed: clickable = $isClickable")
        
        runOnUiThread {
            val message = if (isClickable) "按钮已激活！" else "按钮未激活"
            Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
        }
    }
    
    override fun onClickExecuted(success: Boolean) {
        Log.i(TAG, "Click executed: success = $success")
        
        runOnUiThread {
            val message = if (success) "✅ 点击成功执行！" else "❌ 点击执行失败"
            Toast.makeText(this, message, Toast.LENGTH_LONG).show()
        }
        
        // 点击执行后停止监控
        automationManager.stopMonitoring()
    }
    
    override fun onError(error: String) {
        Log.e(TAG, "Automation error: $error")
        
        runOnUiThread {
            Toast.makeText(this, "错误: $error", Toast.LENGTH_LONG).show()
        }
    }
    
    override fun onDestroy() {
        super.onDestroy()
        automationManager.stopMonitoring()
        webView.destroy()
    }
    
    override fun onBackPressed() {
        if (webView.canGoBack()) {
            webView.goBack()
        } else {
            super.onBackPressed()
        }
    }
}
