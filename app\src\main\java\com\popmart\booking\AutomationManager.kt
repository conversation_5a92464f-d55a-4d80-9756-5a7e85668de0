package com.popmart.booking

import android.os.Handler
import android.os.Looper
import android.util.Log
import android.webkit.WebView
import kotlinx.coroutines.*
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicLong

class AutomationManager {
    private val TAG = "AutomationManager"
    private val isMonitoring = AtomicBoolean(false)
    private val targetTime = AtomicLong(0)
    private var monitoringJob: Job? = null
    private val mainHandler = Handler(Looper.getMainLooper())
    
    // 网络延迟补偿（毫秒）
    private var networkDelayCompensation = 50L
    
    interface AutomationCallback {
        fun onCountdownDetected(remainingMs: Long)
        fun onButtonStateChanged(isClickable: Boolean)
        fun onClickExecuted(success: Boolean)
        fun onError(error: String)
    }
    
    private var callback: AutomationCallback? = null
    
    fun setCallback(callback: AutomationCallback) {
        this.callback = callback
    }
    
    fun startMonitoring(webView: WebView) {
        if (isMonitoring.get()) {
            Log.w(TAG, "Already monitoring")
            return
        }
        
        isMonitoring.set(true)
        Log.i(TAG, "Starting countdown monitoring")
        
        monitoringJob = CoroutineScope(Dispatchers.Main).launch {
            while (isMonitoring.get()) {
                try {
                    checkCountdownAndButton(webView)
                    delay(10) // 10ms检查间隔，确保高精度
                } catch (e: Exception) {
                    Log.e(TAG, "Monitoring error", e)
                    callback?.onError("Monitoring error: ${e.message}")
                }
            }
        }
    }
    
    fun stopMonitoring() {
        isMonitoring.set(false)
        monitoringJob?.cancel()
        Log.i(TAG, "Stopped monitoring")
    }
    
    private suspend fun checkCountdownAndButton(webView: WebView) {
        val script = """
            (function() {
                try {
                    // 查找倒计时元素
                    const timeElement = document.querySelector('[class*="time"], [id*="time"], [class*="countdown"], [id*="countdown"]');
                    const registerButton = document.querySelector('button:contains("Register"), [class*="register"], [id*="register"], button[onclick*="register"]');
                    
                    let remainingTime = -1;
                    let isButtonClickable = false;
                    
                    // 解析倒计时
                    if (timeElement) {
                        const timeText = timeElement.textContent || timeElement.innerText;
                        // 匹配各种时间格式: HH:MM:SS, MM:SS, 或纯秒数
                        const timeMatch = timeText.match(/(\d{1,2}):(\d{2}):(\d{2})|(\d{1,2}):(\d{2})|(\d+)/);
                        if (timeMatch) {
                            if (timeMatch[1] && timeMatch[2] && timeMatch[3]) {
                                // HH:MM:SS格式
                                remainingTime = parseInt(timeMatch[1]) * 3600 + parseInt(timeMatch[2]) * 60 + parseInt(timeMatch[3]);
                            } else if (timeMatch[4] && timeMatch[5]) {
                                // MM:SS格式
                                remainingTime = parseInt(timeMatch[4]) * 60 + parseInt(timeMatch[5]);
                            } else if (timeMatch[6]) {
                                // 纯秒数
                                remainingTime = parseInt(timeMatch[6]);
                            }
                            remainingTime *= 1000; // 转换为毫秒
                        }
                    }
                    
                    // 检查按钮状态
                    if (registerButton) {
                        const computedStyle = window.getComputedStyle(registerButton);
                        const isDisabled = registerButton.disabled || 
                                         registerButton.hasAttribute('disabled') ||
                                         computedStyle.pointerEvents === 'none' ||
                                         computedStyle.opacity < 0.5 ||
                                         registerButton.classList.contains('disabled');
                        isButtonClickable = !isDisabled;
                    }
                    
                    return {
                        remainingTime: remainingTime,
                        isButtonClickable: isButtonClickable,
                        buttonExists: !!registerButton,
                        timestamp: Date.now()
                    };
                } catch (e) {
                    return {
                        error: e.message,
                        timestamp: Date.now()
                    };
                }
            })();
        """.trimIndent()
        
        webView.evaluateJavascript(script) { result ->
            try {
                parseMonitoringResult(result, webView)
            } catch (e: Exception) {
                Log.e(TAG, "Failed to parse monitoring result", e)
            }
        }
    }
    
    private fun parseMonitoringResult(result: String, webView: WebView) {
        // 解析JavaScript返回的JSON结果
        // 这里需要实现JSON解析逻辑
        Log.d(TAG, "Monitoring result: $result")
        
        // 简化的解析逻辑（实际项目中应使用JSON库）
        if (result.contains("remainingTime")) {
            val remainingTimeMatch = Regex("remainingTime\":(\\d+)").find(result)
            val isClickableMatch = Regex("isButtonClickable\":(true|false)").find(result)
            
            remainingTimeMatch?.let { match ->
                val remainingMs = match.groupValues[1].toLongOrNull() ?: -1
                if (remainingMs >= 0) {
                    callback?.onCountdownDetected(remainingMs)
                    
                    // 如果倒计时即将结束，准备精确点击
                    if (remainingMs <= networkDelayCompensation + 100) {
                        prepareForPreciseClick(webView, remainingMs)
                    }
                }
            }
            
            isClickableMatch?.let { match ->
                val isClickable = match.groupValues[1].toBoolean()
                callback?.onButtonStateChanged(isClickable)
                
                // 如果按钮变为可点击状态，立即执行点击
                if (isClickable) {
                    executePreciseClick(webView)
                }
            }
        }
    }
    
    private fun prepareForPreciseClick(webView: WebView, remainingMs: Long) {
        val clickDelay = maxOf(0, remainingMs - networkDelayCompensation)
        
        Log.i(TAG, "Preparing precise click in ${clickDelay}ms")
        
        mainHandler.postDelayed({
            executePreciseClick(webView)
        }, clickDelay)
    }
    
    private fun executePreciseClick(webView: WebView) {
        val clickScript = """
            (function() {
                try {
                    const registerButton = document.querySelector('button:contains("Register"), [class*="register"], [id*="register"], button[onclick*="register"]');
                    if (registerButton && !registerButton.disabled) {
                        // 多种点击方式确保成功
                        registerButton.click();
                        registerButton.dispatchEvent(new MouseEvent('click', {
                            view: window,
                            bubbles: true,
                            cancelable: true
                        }));
                        
                        // 如果是表单提交按钮
                        const form = registerButton.closest('form');
                        if (form) {
                            form.submit();
                        }
                        
                        return {
                            success: true,
                            timestamp: Date.now(),
                            message: 'Click executed successfully'
                        };
                    } else {
                        return {
                            success: false,
                            timestamp: Date.now(),
                            message: 'Button not found or disabled'
                        };
                    }
                } catch (e) {
                    return {
                        success: false,
                        timestamp: Date.now(),
                        error: e.message
                    };
                }
            })();
        """.trimIndent()
        
        webView.evaluateJavascript(clickScript) { result ->
            Log.i(TAG, "Click result: $result")
            val success = result.contains("\"success\":true")
            callback?.onClickExecuted(success)
        }
    }
    
    fun setNetworkDelayCompensation(delayMs: Long) {
        this.networkDelayCompensation = delayMs
        Log.i(TAG, "Network delay compensation set to ${delayMs}ms")
    }
}
